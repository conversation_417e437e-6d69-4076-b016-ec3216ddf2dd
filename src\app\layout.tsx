import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { StructuredData } from "@/components/structured-data"
import {
  getPersonName,
  getPersonEmail,
  getBusinessId,
  getBusinessAddress,
  getMetadataTitle,
  getMetadataDescription,
  getGithubUrl,
  getTwitterHandle
} from "@/lib/env";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: getMetadataTitle(),
  description: getMetadataDescription(),
  keywords: ["cybersecurity", "cloud development", "automation", "n8n", "full stack", "AI", "security"],
  authors: [{ name: get<PERSON>ersonN<PERSON>(), url: getGithubUrl() }],
  creator: get<PERSON>ersonN<PERSON>(),
  publisher: get<PERSON><PERSON><PERSON><PERSON>(),
  openGraph: {
    title: getMetadataTitle(),
    description: getMetadataDescription(),
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary",
    title: getMetadataTitle(),
    description: getMetadataDescription(),
    creator: getTwitterHandle(),
  },
  robots: {
    index: true,
    follow: true,
  },
  verification: {
    other: {
      "business-name": getPersonName(),
      "business-id": getBusinessId(),
      "business-address": getBusinessAddress(),
      "business-email": getPersonEmail(),
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="business-name" content={getPersonName()} />
        <meta name="business-id" content={getBusinessId()} />
        <meta name="business-address" content={getBusinessAddress()} />
        <meta name="business-email" content={getPersonEmail()} />
        <meta name="contact-email" content={getPersonEmail()} />
        <meta name="author" content={getPersonName()} />
        <meta name="theme-color" content="#ffffff" />
        <meta name="theme-color" content="#0a0a0a" media="(prefers-color-scheme: dark)" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  var theme = localStorage.getItem('theme');
                  if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    document.documentElement.classList.add('dark');
                  }
                } catch (e) {}
              })();
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <StructuredData />
        {children}
      </body>
    </html>
  );
}
