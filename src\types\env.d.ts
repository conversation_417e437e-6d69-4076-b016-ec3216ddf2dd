declare namespace NodeJS {
  interface ProcessEnv {
    // Personal Information
    NEXT_PUBLIC_PERSON_NAME: string
    NEXT_PUBLIC_PERSON_EMAIL: string
    NEXT_PUBLIC_PERSON_JOB_TITLE: string
    NEXT_PUBLIC_PERSON_DESCRIPTION: string
    NEXT_PUBLIC_PERSON_WEBSITE_URL: string
    NEXT_PUBLIC_PERSON_PROFILE_IMAGE: string

    // Business Information
    NEXT_PUBLIC_BUSINESS_ID: string
    NEXT_PUBLIC_BUSINESS_ADDRESS: string

    // Social Media Links
    NEXT_PUBLIC_GITHUB_URL: string
    NEXT_PUBLIC_LINKEDIN_URL: string
    NEXT_PUBLIC_TWITTER_URL: string
    NEXT_PUBLIC_TWITTER_HANDLE: string

    // Credly Badge Configuration
    NEXT_PUBLIC_CREDLY_BADGE_COUNT: string
    NEXT_PUBLIC_CREDLY_BADGE_ID_1: string
    NEXT_PUBLIC_CREDLY_BADGE_ID_2?: string
    NEXT_PUBLIC_CREDLY_BADGE_ID_3?: string
    NEXT_PUBLIC_CREDLY_BADGE_ID_4?: string
    NEXT_PUBLIC_CREDLY_BADGE_ID_5?: string
    NEXT_PUBLIC_CREDLY_BADGE_ID_6?: string
    NEXT_PUBLIC_CREDLY_BADGE_ID_7?: string
    NEXT_PUBLIC_CREDLY_BADGE_ID_8?: string
    NEXT_PUBLIC_CREDLY_BADGE_ID_9?: string
    NEXT_PUBLIC_CREDLY_BADGE_ID_10?: string
    // Add more as needed
  }
}
