import { Github, Linkedin, Mail, Twitter } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { getSocialLinks } from "@/lib/env"

// Icon mapping for dynamic icon selection
const iconMap = {
  Github,
  Linkedin,
  Twitter,
  Mail,
}

export function SocialLinks() {
  const socialLinks = getSocialLinks()

  return (
    <div className="flex flex-wrap gap-4 justify-center sm:justify-start">
      {socialLinks.map((link) => {
        const Icon = iconMap[link.icon as keyof typeof iconMap]
        return (
          <Button
            key={link.name}
            variant="outline"
            size="sm"
            asChild
            className="gap-2"
          >
            <a
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={link.label}
            >
              <Icon className="h-4 w-4" />
              {link.name}
            </a>
          </Button>
        )
      })}
    </div>
  )
}
