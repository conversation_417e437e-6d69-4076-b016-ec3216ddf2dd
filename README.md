# Professional One-Page Website Template

A clean, professional, single-page website template built with Next.js 15. Fully configurable with environment variables and ready for open-source use. Perfect for professionals who need a business verification-ready website.

## 🌟 Features

- **Professional Design**: Clean, modern layout with responsive design
- **Light/Dark Theme**: Toggle between light and dark modes
- **Environment Variables**: All personal information configurable via environment variables
- **Open Source Ready**: No hardcoded personal information - perfect for sharing and forking
- **Business Verification Ready**: Includes all required metadata and structured data for Meta Business Verification
- **Accessibility Compliant**: Semantic HTML, ARIA labels, skip links, and keyboard navigation
- **SEO Optimized**: Comprehensive meta tags, Open Graph, and JSON-LD structured data
- **Progressive Enhancement**: Works without JavaScript (graceful degradation)
- **Performance Optimized**: Built with Next.js 15 and optimized for speed

## 📋 Sections

1. **Header/Hero Section**: Configurable personal branding, profile photo, and professional description
2. **Certifications**: Dynamic Credly badge integration (supports 1-10 badges)
3. **Social Media**: Configurable links to GitHub, LinkedIn, X (Twitter), and email with professional icons
4. **Footer**: Configurable business information including business ID, address, and contact details

## 🛠 Tech Stack

- **Framework**: Next.js 15.3.3 with React 19
- **Styling**: Tailwind CSS v4 with shadcn/ui components
- **Icons**: Lucide React
- **Fonts**: Geist Sans and Geist Mono
- **Language**: TypeScript

## 🚀 Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser** and navigate to [http://localhost:3000](http://localhost:3000)

## 📦 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🏗 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and theme variables
│   ├── layout.tsx           # Root layout with metadata and structured data
│   └── page.tsx             # Main page component
├── components/
│   ├── ui/                  # shadcn/ui components
│   │   ├── button.tsx
│   │   └── card.tsx
│   ├── social-links.tsx     # Social media links component
│   ├── structured-data.tsx  # JSON-LD structured data
│   └── theme-toggle.tsx     # Light/dark theme toggle
└── lib/
    └── utils.ts             # Utility functions
```

## 🎨 Design System

The website uses a modern design system with:
- **Colors**: Neutral palette with support for light/dark themes
- **Typography**: Geist font family for clean, professional text
- **Components**: shadcn/ui components for consistency
- **Spacing**: Consistent spacing scale using Tailwind CSS
- **Responsive**: Mobile-first responsive design

## 📱 Business Verification

This website includes all necessary elements for Meta Business Verification:
- Business name, ID (IČO), address, and email in metadata
- Structured data (JSON-LD) for automated verification
- Semantic HTML with proper contact information
- Professional appearance and functionality

## ⚙️ Configuration

### Environment Variables

The website uses environment variables to configure all personal information, making it perfect for open-source sharing:

1. **Copy the example environment file**:
   ```bash
   cp .env.example .env.local
   ```

2. **Configure your personal information**:
   ```bash
   # Personal Information
   NEXT_PUBLIC_PERSON_NAME=Your Full Name
   NEXT_PUBLIC_PERSON_EMAIL=<EMAIL>
   NEXT_PUBLIC_PERSON_JOB_TITLE=Your Job Title
   NEXT_PUBLIC_PERSON_DESCRIPTION=Your professional description here
   NEXT_PUBLIC_PERSON_WEBSITE_URL=https://your-website.com
   NEXT_PUBLIC_PERSON_PROFILE_IMAGE=/your-profile-image.png

   # Business Information
   NEXT_PUBLIC_BUSINESS_ID=your-business-id
   NEXT_PUBLIC_BUSINESS_ADDRESS=Your Business Address

   # Social Media Links
   NEXT_PUBLIC_GITHUB_URL=https://github.com/your-username
   NEXT_PUBLIC_LINKEDIN_URL=https://www.linkedin.com/in/your-profile/
   NEXT_PUBLIC_TWITTER_URL=https://x.com/your-handle
   NEXT_PUBLIC_TWITTER_HANDLE=@your-handle

   # Credly Badge Configuration
   NEXT_PUBLIC_CREDLY_BADGE_COUNT=1
   NEXT_PUBLIC_CREDLY_BADGE_ID_1=your-badge-id-here
   # Add more badges as needed...
   ```

3. **Find your Credly Badge ID**:
   - Go to your Credly profile
   - Click on a badge
   - Copy the ID from the URL: `https://www.credly.com/badges/[BADGE-ID-HERE]`

## 🌐 Deployment

The website is ready for deployment on any platform that supports Next.js:
- **Vercel** (recommended)
- **Netlify**
- **AWS Amplify**
- **Self-hosted**

**Note**: Remember to set your environment variables in your deployment platform's settings.

## 🚀 Quick Start for Your Own Website

1. **Fork or clone this repository**
2. **Copy `.env.example` to `.env.local`**
3. **Update all environment variables with your information**
4. **Replace the profile image** in the `public` folder
5. **Deploy to your preferred platform**

## 📄 License

This project is open source and available under the MIT License. Feel free to use it for your own professional website!
