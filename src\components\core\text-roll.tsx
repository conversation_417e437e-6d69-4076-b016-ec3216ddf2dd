'use client';

import { TextRoll } from '@/components/ui/text-roll';
import { useTheme } from '@/hooks/use-theme';
import { getPersonName } from '@/lib/env';

export function TextRollBasic() {
  const theme = useTheme();

  return (
    <TextRoll
      key={theme} // This will trigger re-animation when theme changes
      className='text-4xl text-foreground'
    >
      {getPersonName()}
    </TextRoll>
  );
}
