import { CredlyBadge } from "./credly-badge"

interface CredlyBadgeConfig {
  id: string
  index: number
}

export function CredlyBadgesSection() {
  // Get the number of badges to display from environment
  const badgeCount = parseInt(process.env.NEXT_PUBLIC_CREDLY_BADGE_COUNT || "1", 10)

  // Collect all badge IDs from environment variables
  const badges: CredlyBadgeConfig[] = []

  for (let i = 1; i <= badgeCount; i++) {
    const badgeId = process.env[`NEXT_PUBLIC_CREDLY_BADGE_ID_${i}`]
    if (badgeId) {
      badges.push({
        id: badgeId,
        index: i
      })
    }
  }

  // If no badges are configured, don't render anything
  if (badges.length === 0) {
    return null
  }

  return (
    <div className="grid gap-6 justify-items-center"
         style={{
           gridTemplateColumns: badges.length === 1
             ? '1fr'
             : badges.length === 2
             ? 'repeat(2, 1fr)'
             : 'repeat(auto-fit, minmax(200px, 1fr))'
         }}>
      {badges.map((badge) => (
        <div key={badge.id} className="flex justify-center">
          <CredlyBadge badgeId={badge.id} />
        </div>
      ))}
    </div>
  )
}
