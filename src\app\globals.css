@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: rgb(252, 252, 252);
  --foreground: rgb(23, 23, 23);
  --card: rgb(252, 252, 252);
  --card-foreground: rgb(23, 23, 23);
  --popover: rgb(252, 252, 252);
  --popover-foreground: rgb(82, 82, 82);
  --primary: rgb(114, 227, 173);
  --primary-foreground: rgb(30, 39, 35);
  --secondary: rgb(253, 253, 253);
  --secondary-foreground: rgb(23, 23, 23);
  --muted: rgb(237, 237, 237);
  --muted-foreground: rgb(32, 32, 32);
  --accent: rgb(237, 237, 237);
  --accent-foreground: rgb(32, 32, 32);
  --destructive: rgb(202, 50, 20);
  --destructive-foreground: rgb(255, 252, 252);
  --border: rgb(223, 223, 223);
  --input: rgb(246, 246, 246);
  --ring: rgb(114, 227, 173);
  --chart-1: rgb(114, 227, 173);
  --chart-2: rgb(59, 130, 246);
  --chart-3: rgb(139, 92, 246);
  --chart-4: rgb(245, 158, 11);
  --chart-5: rgb(16, 185, 129);
  --sidebar: rgb(252, 252, 252);
  --sidebar-foreground: rgb(112, 112, 112);
  --sidebar-primary: rgb(114, 227, 173);
  --sidebar-primary-foreground: rgb(30, 39, 35);
  --sidebar-accent: rgb(237, 237, 237);
  --sidebar-accent-foreground: rgb(32, 32, 32);
  --sidebar-border: rgb(223, 223, 223);
  --sidebar-ring: rgb(114, 227, 173);
  --badge-background: rgb(255, 255, 255);
  --font-sans: Outfit, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.09);
  --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 2px 4px -1px hsl(0 0% 0% / 0.17);
  --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 4px 6px -1px hsl(0 0% 0% / 0.17);
  --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 8px 10px -1px hsl(0 0% 0% / 0.17);
  --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.43);
  --tracking-normal: 0.025em;
}

.dark {
  --background: rgb(18, 18, 18);
  --foreground: rgb(226, 232, 240);
  --card: rgb(23, 23, 23);
  --card-foreground: rgb(226, 232, 240);
  --popover: rgb(36, 36, 36);
  --popover-foreground: rgb(169, 169, 169);
  --primary: rgb(0, 98, 57);
  --primary-foreground: rgb(221, 232, 227);
  --secondary: rgb(36, 36, 36);
  --secondary-foreground: rgb(250, 250, 250);
  --muted: rgb(31, 31, 31);
  --muted-foreground: rgb(162, 162, 162);
  --accent: rgb(49, 49, 49);
  --accent-foreground: rgb(250, 250, 250);
  --destructive: rgb(84, 28, 21);
  --destructive-foreground: rgb(237, 233, 232);
  --border: rgb(41, 41, 41);
  --input: rgb(36, 36, 36);
  --ring: rgb(74, 222, 128);
  --chart-1: rgb(74, 222, 128);
  --chart-2: rgb(96, 165, 250);
  --chart-3: rgb(167, 139, 250);
  --chart-4: rgb(251, 191, 36);
  --chart-5: rgb(45, 212, 191);
  --sidebar: rgb(18, 18, 18);
  --sidebar-foreground: rgb(137, 137, 137);
  --sidebar-primary: rgb(0, 98, 57);
  --sidebar-primary-foreground: rgb(221, 232, 227);
  --sidebar-accent: rgb(49, 49, 49);
  --sidebar-accent-foreground: rgb(250, 250, 250);
  --sidebar-border: rgb(41, 41, 41);
  --sidebar-ring: rgb(74, 222, 128);
  --badge-background: rgb(255, 255, 255);
  --font-sans: Outfit, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.09);
  --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17);
  --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 2px 4px -1px hsl(0 0% 0% / 0.17);
  --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 4px 6px -1px hsl(0 0% 0% / 0.17);
  --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 8px 10px -1px hsl(0 0% 0% / 0.17);
  --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.43);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-badge-background: var(--badge-background);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
