/**
 * Environment variable utilities with fallbacks for open-source compatibility
 */

// Personal Information
export const getPersonName = () => 
  process.env.NEXT_PUBLIC_PERSON_NAME || "Your Name"

export const getPersonEmail = () => 
  process.env.NEXT_PUBLIC_PERSON_EMAIL || "<EMAIL>"

export const getPersonJobTitle = () => 
  process.env.NEXT_PUBLIC_PERSON_JOB_TITLE || "Your Job Title"

export const getPersonDescription = () => 
  process.env.NEXT_PUBLIC_PERSON_DESCRIPTION || "Your professional description here"

export const getPersonWebsiteUrl = () => 
  process.env.NEXT_PUBLIC_PERSON_WEBSITE_URL || "https://your-website.com"

export const getPersonProfileImage = () => 
  process.env.NEXT_PUBLIC_PERSON_PROFILE_IMAGE || "/profile-placeholder.svg"

// Business Information
export const getBusinessId = () => 
  process.env.NEXT_PUBLIC_BUSINESS_ID || "your-business-id"

export const getBusinessAddress = () => 
  process.env.NEXT_PUBLIC_BUSINESS_ADDRESS || "Your Business Address"

// Social Media Links
export const getGithubUrl = () => 
  process.env.NEXT_PUBLIC_GITHUB_URL || "https://github.com/your-username"

export const getLinkedinUrl = () => 
  process.env.NEXT_PUBLIC_LINKEDIN_URL || "https://www.linkedin.com/in/your-profile/"

export const getTwitterUrl = () => 
  process.env.NEXT_PUBLIC_TWITTER_URL || "https://x.com/your-handle"

export const getTwitterHandle = () => 
  process.env.NEXT_PUBLIC_TWITTER_HANDLE || "@your-handle"

// Metadata helpers
export const getMetadataTitle = () => 
  `${getPersonName()} - ${getPersonJobTitle()}`

export const getMetadataDescription = () => 
  getPersonDescription()

// Social links configuration
export const getSocialLinks = () => [
  {
    name: "GitHub",
    url: getGithubUrl(),
    icon: "Github",
    label: `Visit ${getPersonName()}'s GitHub profile`,
  },
  {
    name: "LinkedIn",
    url: getLinkedinUrl(),
    icon: "Linkedin",
    label: `Connect with ${getPersonName()} on LinkedIn`,
  },
  {
    name: "X (Twitter)",
    url: getTwitterUrl(),
    icon: "Twitter",
    label: `Follow ${getPersonName()} on X (Twitter)`,
  },
  {
    name: "Email",
    url: `mailto:${getPersonEmail()}`,
    icon: "Mail",
    label: `Send an email to ${getPersonName()}`,
  },
]

// Structured data helper
export const getStructuredData = () => ({
  "@context": "https://schema.org",
  "@type": "Person",
  "name": getPersonName(),
  "jobTitle": getPersonJobTitle(),
  "description": getPersonDescription(),
  "url": getPersonWebsiteUrl(),
  "email": getPersonEmail(),
  "sameAs": [
    getGithubUrl(),
    getLinkedinUrl(),
    getTwitterUrl()
  ],
  "address": {
    "@type": "PostalAddress",
    "streetAddress": getBusinessAddress().split(',')[0]?.trim() || "",
    "addressLocality": getBusinessAddress().split(',')[1]?.trim() || "",
    "postalCode": getBusinessAddress().split(',')[2]?.trim() || "",
    "addressCountry": "CZ"
  },
})
